{"name": "recoin", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "vite --port 5229", "check": "tsgo --project ./tsconfig.json", "build": "pnpm check && vite build", "preview": "vite preview", "deploy": "wrangler deploy"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@cloudflare/vite-plugin": "^1.5.0", "@cloudflare/workers-types": "^4.20250607.0", "@tailwindcss/vite": "^4.1.5", "@types/node": "^22.15.29", "@typescript/native-preview": "7.0.0-dev.20250608.1", "tailwindcss": "^4.1.8", "vite": "npm:rolldown-vite@latest", "wrangler": "^4.18.0"}, "dependencies": {"@hono/zod-validator": "^0.7.0", "hono": "^4.7.11", "retend": "https://pkg.pr.new/resuite/retend@904dfd3", "retend-server": "https://pkg.pr.new/resuite/retend/retend-server@904dfd3", "retend-utils": "https://pkg.pr.new/resuite/retend/retend-utils@904dfd3", "zod": "^3.25.46"}}